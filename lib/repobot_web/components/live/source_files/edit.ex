defmodule RepobotWeb.Live.SourceFiles.Edit do
  use Repobot<PERSON>eb, :live_view

  alias <PERSON>obot.SourceFiles
  alias RepobotWeb.Live.SourceFiles.FormComponent

  def mount(%{"id" => id}, _session, socket) do
    source_file = SourceFiles.get_source_file!(id)

    {:ok,
     socket
     |> assign(:source_file, source_file)
     |> assign(:page_title, source_file.name)}
  end

  def handle_info({:source_file_updated, source_file}, socket) do
    {:noreply,
     socket
     |> assign(:source_file, source_file)
     |> put_flash(:info, "Source file updated successfully")}
  end

  def handle_event("toggle_template", _params, socket) do
    {:noreply, socket}
  end

  def render(assigns) do
    ~H"""
    <div class="p-6">
      <div class="mb-8">
        <nav class="mb-6">
          <ol role="list" class="flex items-center space-x-2 text-sm text-slate-500">
            <li>
              <.link navigate={~p"/source-files"} class="hover:text-indigo-600">
                Source Files
              </.link>
            </li>
            <li>•</li>
            <li>
              <.link navigate={~p"/source-files/#{@source_file.id}"} class="hover:text-indigo-600">
                {@source_file.name}
              </.link>
            </li>
            <li>•</li>
            <li class="font-medium text-slate-900">Edit</li>
          </ol>
        </nav>
        <div>
          <h1 class="text-2xl font-semibold text-slate-900">Edit Source File</h1>
          <p class="mt-2 text-sm text-slate-600">Update your source file content and settings.</p>
        </div>
      </div>

      <div class="bg-white rounded-lg shadow-sm border border-slate-200">
        <.live_component
          module={FormComponent}
          id="edit-source-file"
          action={:edit}
          source_file={@source_file}
          current_user={@current_user}
        />
      </div>
    </div>
    """
  end
end
