// If you want to use Phoenix channels, run `mix help phx.gen.channel`
// to get started and then uncomment the line below.
// import "./user_socket.js"

// You can include dependencies in two ways.
//
// The simplest option is to put them in assets/vendor and
// import them using relative paths:
//
//     import "../vendor/some-package.js"
//
// Alternatively, you can `npm install some-package --prefix assets` and import
// them using a path starting with the package name:
//
//     import "some-package"
//

// Include phoenix_html to handle method=PUT/DELETE in forms and buttons.
import "phoenix_html"
// Establish Phoenix Socket and LiveView configuration.
import { Socket } from "phoenix"
import { LiveSocket } from "phoenix_live_view"
import topbar from "../vendor/topbar"
import * as Diff2Html from "diff2html"
import * as Diff from "diff"

import UpdateApiKey from "./hooks/update_api_key"
import { RepositoryList } from "./hooks/repository_list"
import SyncMap from "./hooks/sync_map"
import RepositoryDragDrop from "./hooks/repository_drag_drop"

let Hooks = {
  Diff,
  UpdateApi<PERSON>ey,
  RepositoryList,
  SyncMap,
  RepositoryDragDrop
}

Hooks.Diff = {
  mounted() {
    this.handleEvent("render_diff", ({ source, target }) => {
      const diff = Diff.createPatch("file", target, source)
      const diffHtml = Diff2Html.html(diff, {
        drawFileList: false,
        matching: 'words',
        outputFormat: 'side-by-side'
      })
      this.el.innerHTML = diffHtml
    })
  }
}

let csrfToken = document.querySelector("meta[name='csrf-token']").getAttribute("content")
let liveSocket = new LiveSocket("/live", Socket, {
  longPollFallbackMs: 2500,
  params: { _csrf_token: csrfToken },
  hooks: Hooks
})

// Show progress bar on live navigation and form submits
topbar.config({ barColors: { 0: "#29d" }, shadowColor: "rgba(0, 0, 0, .3)" })
window.addEventListener("phx:page-loading-start", _info => topbar.show(300))
window.addEventListener("phx:page-loading-stop", _info => topbar.hide())

// connect if there are any LiveViews on the page
liveSocket.connect()

// expose liveSocket on window for web console debug logs and latency simulation:
// >> liveSocket.enableDebug()
// >> liveSocket.enableLatencySim(1000)  // enabled for duration of browser session
// >> liveSocket.disableLatencySim()
window.liveSocket = liveSocket

// Add clipboard copy functionality
window.addEventListener("clipboard-copy", (event) => {
  const button = event.target.closest('button');
  if (!button) return;

  const targetId = button.dataset.copyTarget;
  if (!targetId) return;

  const textElement = document.getElementById(targetId);
  if (!textElement) return;

  navigator.clipboard.writeText(textElement.textContent).then(() => {
    // Show a brief visual feedback
    const originalColor = button.style.color;
    button.style.color = "#059669"; // text-green-600

    setTimeout(() => {
      button.style.color = originalColor;
    }, 1000);
  }).catch((err) => {
    console.error("Failed to copy text: ", err);
  });
});

// Add URL opening functionality for LiveView events
window.addEventListener("phx:open_url", (event) => {
  const { url } = event.detail;
  if (url) {
    window.open(url, '_blank', 'noopener,noreferrer');
  }
});

