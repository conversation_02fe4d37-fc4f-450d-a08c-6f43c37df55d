defmodule Repobot.AITest do
  use Repobot.DataCase

  alias <PERSON>obot.AI
  alias Repobot.Accounts.{Organization, OrganizationSettings}

  describe "backend/1 in test environment" do
    test "always returns the configured mock in test environment" do
      # In test environment, should always return the mock regardless of API keys
      assert AI.backend() == Repobot.Test.AIMock
    end

    test "returns mock even with organization API keys in test environment" do
      organization = %Organization{
        settings: %OrganizationSettings{
          anthropic_api_key: "org-anthropic-key",
          openai_api_key: "org-openai-key"
        }
      }

      # Should still return mock in test environment
      assert AI.backend(organization) == Repobot.Test.AIMock
    end
  end

  describe "backend selection logic (unit tests)" do
    # These tests verify the internal logic without actually calling backend/1
    # since that always returns the mock in test environment

    test "organization key detection works correctly" do
      organization_with_anthropic = %Organization{
        settings: %OrganizationSettings{
          anthropic_api_key: "org-anthropic-key",
          openai_api_key: nil
        }
      }

      organization_with_openai = %Organization{
        settings: %OrganizationSettings{
          anthropic_api_key: nil,
          openai_api_key: "org-openai-key"
        }
      }

      organization_with_both = %Organization{
        settings: %OrganizationSettings{
          anthropic_api_key: "org-anthropic-key",
          openai_api_key: "org-openai-key"
        }
      }

      organization_with_empty = %Organization{
        settings: %OrganizationSettings{
          anthropic_api_key: "",
          openai_api_key: ""
        }
      }

      organization_with_nil = %Organization{settings: nil}

      assert AI.test_has_organization_anthropic_key?(organization_with_anthropic) == true
      assert AI.test_has_organization_openai_key?(organization_with_anthropic) == false

      assert AI.test_has_organization_anthropic_key?(organization_with_openai) == false
      assert AI.test_has_organization_openai_key?(organization_with_openai) == true

      assert AI.test_has_organization_anthropic_key?(organization_with_both) == true
      assert AI.test_has_organization_openai_key?(organization_with_both) == true

      assert AI.test_has_organization_anthropic_key?(organization_with_empty) == false
      assert AI.test_has_organization_openai_key?(organization_with_empty) == false

      assert AI.test_has_organization_anthropic_key?(organization_with_nil) == false
      assert AI.test_has_organization_openai_key?(organization_with_nil) == false
    end

    test "environment variable detection works correctly" do
      # Clear environment variables first
      System.delete_env("OPENAI_API_KEY")
      System.delete_env("ANTHROPIC_API_KEY")

      assert AI.test_has_env_openai_key?() == false
      assert AI.test_has_env_anthropic_key?() == false

      # Set OpenAI key
      System.put_env("OPENAI_API_KEY", "test-openai-key")
      assert AI.test_has_env_openai_key?() == true
      assert AI.test_has_env_anthropic_key?() == false

      # Set Anthropic key
      System.put_env("ANTHROPIC_API_KEY", "test-anthropic-key")
      assert AI.test_has_env_openai_key?() == true
      assert AI.test_has_env_anthropic_key?() == true

      # Test empty string handling
      System.put_env("OPENAI_API_KEY", "")
      assert AI.test_has_env_openai_key?() == false
    end

    test "backend selection prioritizes correctly" do
      # Clear environment variables
      System.delete_env("OPENAI_API_KEY")
      System.delete_env("ANTHROPIC_API_KEY")

      # Test with organization having Anthropic key
      org_with_anthropic = %Organization{
        settings: %OrganizationSettings{
          anthropic_api_key: "org-anthropic-key",
          openai_api_key: nil
        }
      }

      assert AI.test_select_backend_by_api_key_availability(org_with_anthropic) ==
               Repobot.AI.Anthropic

      # Test with organization having both keys (should prioritize Anthropic)
      org_with_both = %Organization{
        settings: %OrganizationSettings{
          anthropic_api_key: "org-anthropic-key",
          openai_api_key: "org-openai-key"
        }
      }

      assert AI.test_select_backend_by_api_key_availability(org_with_both) == Repobot.AI.Anthropic

      # Test with organization having only OpenAI key
      org_with_openai = %Organization{
        settings: %OrganizationSettings{
          anthropic_api_key: nil,
          openai_api_key: "org-openai-key"
        }
      }

      assert AI.test_select_backend_by_api_key_availability(org_with_openai) == Repobot.AI.OpenAI

      # Test with no organization keys but environment variables
      System.put_env("OPENAI_API_KEY", "env-openai-key")

      org_with_no_keys = %Organization{
        settings: %OrganizationSettings{
          anthropic_api_key: nil,
          openai_api_key: nil
        }
      }

      assert AI.test_select_backend_by_api_key_availability(org_with_no_keys) == Repobot.AI.OpenAI

      # Test with both environment variables (should prioritize OpenAI)
      System.put_env("ANTHROPIC_API_KEY", "env-anthropic-key")
      assert AI.test_select_backend_by_api_key_availability(org_with_no_keys) == Repobot.AI.OpenAI

      # Test with no keys at all
      System.delete_env("OPENAI_API_KEY")
      System.delete_env("ANTHROPIC_API_KEY")
      assert AI.test_select_backend_by_api_key_availability(org_with_no_keys) == nil
    end
  end

  describe "static_backend/0" do
    test "returns the configured backend" do
      # In test environment, this should be AIMock
      assert AI.static_backend() == Repobot.Test.AIMock
    end
  end
end
